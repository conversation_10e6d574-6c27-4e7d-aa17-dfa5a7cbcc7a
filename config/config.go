package config

import (
	"os"
	"strconv"
	"strings"
	"time"
)

type Config struct {
	MicroBus MicroBusConfig
	MQTT     MQTTConfig
	Hub      HubConfig
	Log      LogConfig
	Redis    RedisConfig
}

type HubConfig struct {
	Group         string // Hub group name
	ClusterEnable bool
	Timeout       int // Request timeout in seconds
}

type MicroBusConfig struct {
	Brokers   string
	QueueSize int
}

type MQTTConfig struct {
	Broker   string
	ClientID string
	Username string
	Password string
	CertFile string
	PrivKey  string
	QoS      byte
}

// LogConfig holds logging configuration
type LogConfig struct {
	Level  string
	Format string // json or console
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Enabled         bool   // Whether Redis is enabled
	Address         string // Single Redis address
	Password        string // Single Redis password
	DB              int    // Redis database number
	ClusterEnabled  bool   // Whether to use Redis cluster
	ClusterAddress  string // Redis cluster addresses (semicolon separated)
	ClusterPassword string // Redis cluster password
	PoolSize        int    // Connection pool size
	MinIdleConns    int    // Minimum idle connections
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	cfg := &Config{
		MicroBus: MicroBusConfig{
			Brokers:   getString("KAFKA_BROKERS", "localhost:9092"),
			QueueSize: getInt("MICROBUS_QUEUE_SIZE", 100),
		},
		MQTT: MQTTConfig{
			Broker:   getString("MQTT_BROKERS", "tcp://localhost:1883"),
			ClientID: getString("MQTT_CLIENT_ID", "beech-forwarder-"+strconv.FormatInt(time.Now().Unix(), 10)),
			Username: getString("MQTT_USER", ""),
			Password: getString("MQTT_PASSWORD", ""),
			CertFile: getString("MQTT_CERT", ""),
			PrivKey:  getString("MQTT_PRIV_KEY", ""),
			QoS:      byte(getInt("MQTT_QOS", 0)),
		},
		Hub: HubConfig{
			Group:         getString("HUB_GROUP", "hub"),
			ClusterEnable: getString("HUB_CLUSTER_ENABLE", "enable") == "enable",
			Timeout:       getInt("HUB_TIMEOUT", 30),
		},
		Log: LogConfig{
			Level:  getString("LOG_LEVEL", "info"),
			Format: getString("LOG_FORMAT", "json"),
		},
		Redis: RedisConfig{
			Enabled:         getBool("REDIS_ENABLED", false),
			Address:         getString("REDIS_ADDRESS", ""),
			Password:        getString("REDIS_PASSWORD", ""),
			DB:              getInt("REDIS_DB", 0),
			ClusterEnabled:  getBool("REDIS_CLUSTER_ENABLED", false),
			ClusterAddress:  getString("REDIS_CLUSTER_ADDRESS", ""),
			ClusterPassword: getString("REDIS_CLUSTER_PASSWORD", ""),
			PoolSize:        getInt("REDIS_POOL_SIZE", 128),
			MinIdleConns:    getInt("REDIS_MIN_IDLE_CONNS", 32),
		},
	}

	return cfg, nil
}

// Helper functions to get environment variables with defaults

func getString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		return strings.ToLower(value) == "true" || value == "1"
	}
	return defaultValue
}

func getStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}
