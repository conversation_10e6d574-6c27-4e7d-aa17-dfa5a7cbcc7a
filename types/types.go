package types

type MqttRequest struct {
	ID    uint32 `json:"id"`    // 生成 uint32 类型唯一 id，用于确认请求和返回对应
	Frame string `json:"frame"` // base64 编码的数据字符串，原始数据为十六进制格式如 "010300000002C40B"
}

type SetResponse struct {
	ID    uint32 `json:"id"`    // response 中的 id 与 request 中的 id 相同
	Errno int    `json:"errno"` // 错误码，0表示成功
}

type GetResponse struct {
	ID    uint32 `json:"id"`    // response 中的 id 与 request 中的 id 相同
	Errno int    `json:"errno"` // 错误码，0表示成功
	Frame string `json:"frame"` // base64 编码的响应数据字符串，原始数据为十六进制格式
}

type RealData struct {
	AcFanEnable                          any `json:"acFanEnable"`                          // 交流风扇使能
	DcFanOFFSpeed                        any `json:"dcFanOFFSpeed"`                        // 直流风扇转速
	DcWaterPumpOFFFlowRate               any `json:"dcWaterPumpOFFFlowRate"`               // 直流水泵流量(主机泵)
	DcCompressorOFFFrequency             any `json:"dcCompressorOFFFrequency"`             // 直流压缩机
	FourWayValve                         any `json:"four-wayValve"`                        // 四通阀状态
	HighPressSwitch                      any `json:"highPressSwitch"`                      // 高压开关状态
	LowPressSwitch                       any `json:"lowPressSwitch"`                       // 低压开关状态
	RefrigerationAntifreezeSwitch        any `json:"refrigerationAntifreezeSwitch"`        // 制冷防冻开关状态
	FlowSwitch                           any `json:"flowSwitch"`                           // 流量开关状态
	FlowMeter                            any `json:"flowMeter"`                            // 流量计
	LinkageSwitch                        any `json:"linkageSwitch"`                        // 联动开关状态
	InterLockSwitch                      any `json:"interLockSwitch"`                      // 联锁开关状态
	HotWaterElectricHeatingZone          any `json:"hotWaterElectricHeatingZone"`          // 热水电加热使能状态
	OilTemperatureHeatingZone            any `json:"oilTemperatureHeatingZone"`            // 油温加热区
	AirConditioningElectricHeating       any `json:"airConditioningElectricHeating"`       // 空调电加热
	ChassisHeatingBelt                   any `json:"chassisHeatingBelt"`                   // 底盘加热带
	Exv1                                 any `json:"exv1"`                                 // 主电子膨胀阀的开度
	Exv2                                 any `json:"exv2"`                                 // 辅助电子膨胀阀的开度
	AmbientTemperature                   any `json:"ambientTemperature"`                   // 环境温度（TAO）
	OutletWaterTemp                      any `json:"outletWaterTemp"`                      // 出水温度（TWO）
	ReturnWaterTemp                      any `json:"returnWaterTemp"`                      // 回水温度（TWI）
	FinTemp                              any `json:"finTemp"`                              // 翅片温度（TRC）
	ExhaustGasTemp                       any `json:"exhaustGasTemp"`                       // 排气温度（TRD）
	SuctionTemp                          any `json:"suctionTemp"`                          // 吸气温度（TRS）
	EvaporationTemp                      any `json:"evaporationTemp"`                      // 蒸发温度（TE）
	SupplementaryAirInletTemp            any `json:"supplementaryAirInletTemp"`            // 补充空气入口温度 (TRI)
	SupplementaryAirOutletTemp           any `json:"supplementaryAirOutletTemp"`           // 送风出口温度（TRO）
	BoardReplacementTemp                 any `json:"boardReplacementTemp"`                 // 板更换温度传感器 (TRP)
	CoilTemp                             any `json:"coilTemp"`                             // 线圈温度传感器 (TRC)
	PcbTemp                              any `json:"pcbTemp"`                              // PCB板温度（PCB）
	PowerSupplyVoltageValue              any `json:"powerSupplyVoltageValue"`              // 电源电压值
	LowPressureValue                     any `json:"lowPressureValue"`                     // 低压压力传感器压力值
	HighPressureValue                    any `json:"highPressureValue"`                    // 高压压力传感器压力值
	RefrigerantLeakValue                 any `json:"refrigerantLeakValue"`                 // 制冷剂泄漏传感器
	ExhaustSaturationTemp                any `json:"exhaustSaturationTemp"`                // 排气饱和温度
	ExhaustPressure                      any `json:"exhaustPressure"`                      // 排气压力值
	SuctionSaturationTemp                any `json:"suctionSaturationTemp"`                // 吸气饱和温度
	InspiratoryPressure                  any `json:"inspiratoryPressure"`                  // 吸气压力值
	DriverSwitchStatus                   any `json:"driverSwitchStatus"`                   // 驱动器开关状态
	SwitchStatus                         any `json:"switchStatus"`                         // 开关状态
	DriverRunningFrequency               any `json:"driverRunningFrequency"`               // 驱动器运行频率
	StableFrequencyState                 any `json:"stableFrequencyState"`                 // 稳频状态
	UpFrequencyStatus                    any `json:"upFrequencyStatus"`                    // 升频状态
	DownFrequencyStatus                  any `json:"downFrequencyStatus"`                  // 降频状态
	BusVolt                              any `json:"busVolt"`                              // 母线电压有效值
	CompressorPhaseCurrentEffectiveValue any `json:"compressorPhaseCurrentEffectiveValue"` // 压缩机相电流有效值
	AcInputVolt                          any `json:"acInputVolt"`                          // 交流输入电压
	AcInputCurrent                       any `json:"acInputCurrent"`                       // 交流输入电流
	IPMTemperature                       any `json:"IPMTemperature"`                       // IPM温度
	PFCModuleTemperature                 any `json:"PFCModuleTemperature"`                 // PFC模块温度
	CompressorCumulativeRunningTime      any `json:"compressorCumulativeRunningTime"`      // 压缩机累计运行时间
	DSPModuleSoftwareVersion             any `json:"DSPModuleSoftwareVersion"`             // DSP&PFC软件版本
	CommSoftwareVersion                  any `json:"commSoftwareVersion"`                  // 通讯版本
	CompressorCode                       any `json:"compressorCode"`                       // 压缩机代码
	EEPROMEncoding                       any `json:"EEPROMEncoding"`                       // EEPROM编码
	SolenoidThreeWayValveStatus          any `json:"SolenoidThree-wayValveStatus"`         // 电磁三通阀（热水、空调切换）
	InletWaterSolenoidValveStatus        any `json:"InletWaterSolenoidValveStatus"`        // 进水电磁阀
	Z1ZoneWaterPump                      any `json:"Z1ZoneWaterPump"`                      // Z1区域水泵
	Z2ZoneWaterPump                      any `json:"Z2ZoneWaterPump"`                      // Z2区域水泵
	Z3ZoneWaterPump                      any `json:"Z3ZoneWaterPump"`                      // Z3区域水泵
	Z2ZoneMixingValue                    any `json:"Z2ZoneMixingValue"`                    // Z2区域混合阀
	Z3ZoneMixingValue                    any `json:"Z3ZoneMixingValue"`                    // Z3区域混合阀
	Z1ZoneInletWaterTemp                 any `json:"z1ZoneInletWaterTemp"`                 // Z1区进水温度
	Z2ZoneInletWaterTemp                 any `json:"z2ZoneInletWaterTemp"`                 // Z2区进水温度
	Z3ZoneInletWaterTemp                 any `json:"z3ZoneInletWaterTemp"`                 // Z3区进水温度
	ReturnWaterPumpStatus                any `json:"returnWaterPumpStatus"`                // 回水泵状态
	TotalOutputWater                     any `json:"totalOutputWater"`                     // 机组总产水量（1#）
	TotalReturnWater                     any `json:"totalReturnWater"`                     // 机组总回水量（1#)
	NumberOfModule                       any `json:"numberOfModule"`                       // 联机数量
	ModuleOperatingStatus                any `json:"moduleOperatingStatus"`                // 联机单元运行状态
	WorkMode                             any `json:"workMode"`                             // 工作模式
	UnitWorkStatus                       any `json:"unitWorkStatus"`                       // 机组工作状态
	EnergyModel                          any `json:"energyModel"`                          // 能量模式
	CoolingRealTimeProduction            any `json:"coolingReal-timeProduction"`           // 制冷实时功率
	CoolingRealTimeReProduction          any `json:"coolingReal-timeReProduction"`         // 制冷实时放热功率
	CoolingRealTimeConsumption           any `json:"coolingReal-timeConsumption"`          // 制冷实时消耗功率
	CoolingRealTimeCOPEER                any `json:"coolingReal-timeCOPEER"`               // 制冷实时能效比
	CoolingTotalProduction               any `json:"coolingTotalProduction"`               // 制冷总能量
	CoolingTotalReProduction             any `json:"coolingTotalReProduction"`             // 制冷总放热能量
	CoolingTotalConsumption              any `json:"coolingTotalConsumption"`              // 制冷总消耗能量
	CoolingTotalCOPEER                   any `json:"coolingTotalCOPEER"`                   // 制冷总能效比
	HeatingRealTimeProduction            any `json:"heatingReal-timeProduction"`           // 制热实时功率
	HeatingRealTimeReProduction          any `json:"heatingReal-timeReProduction"`         // 制热实时吸热功率
	HeatingRealTimeConsumption           any `json:"heatingReal-timeConsumption"`          // 制热实时消耗功率
	HeatingRealTimeCOPEER                any `json:"heatingReal-timeCOPEER"`               // 制热实时能效比
	HeatingTotalProduction               any `json:"heatingTotalProduction"`               // 制热总能量
	HeatingTotalReProduction             any `json:"heatingTotalReProduction"`             // 制热总吸热能量
	HeatingTotalConsumption              any `json:"heatingTotalConsumption"`              // 制热总消耗能量
	HeatingTotalCOPEER                   any `json:"heatingTotalCOPEER"`                   // 制热总能效比
	DhwRealTimeProduction                any `json:"dhwReal-timeProduction"`               // 热水实时功率
	DhwRealTimeReProduction              any `json:"dhwReal-timeReProduction"`             // 热水实时吸热功率
	DhwRealTimeConsumption               any `json:"dhwReal-timeConsumption"`              // 热水实时消耗功率
	DhwRealTimeCOPEER                    any `json:"dhwReal-timeCOPEER"`                   // 热水实时能效比
	DhwTotalProduction                   any `json:"dhwTotalProduction"`                   // 热水总能量
	DhwTotalReProduction                 any `json:"dhwTotalReProduction"`                 // 热水总吸热能量
	DhwTotalConsumption                  any `json:"dhwTotalConsumption"`                  // 热水总消耗能量
	DhwTotalCOPEER                       any `json:"dhwTotalCOPEER"`                       // 热水总能效比
	TotalRealTimeProduction              any `json:"totalReal-timeProduction"`             // 系统实时功率
	TotalRealTimeReProduction            any `json:"totalReal-timeReProduction"`           // 系统实时吸收/放热功率
	TotalRealTimeConsumption             any `json:"totalReal-timeConsumption"`            // 系统实时消耗功率
	TotalRealTimeCOPEER                  any `json:"totalReal-timeCOPEER"`                 // 系统实时能效比
	TotalTotalProduction                 any `json:"totalTotalProduction"`                 // 系统总能量
	TotalTotalReProduction               any `json:"totalTotalReProduction"`               // 系统总吸收/放热能量
	TotalTotalConsumption                any `json:"totalTotalConsumption"`                // 系统消耗能量
	TotalTotalCOPEER                     any `json:"totalTotalCOPEER"`                     // 系统总能效比
	CompressorStarts                     any `json:"compressorStarts"`                     // 压缩机启停总次数
	WaterTankTemp                        any `json:"waterTankTemp"`
	CurrentRunMode                       any `json:"currentRunMode"`
	Fault1                               any `json:"fault1"`
	Fault2                               any `json:"fault2"`
	Fault3                               any `json:"fault3"`
	Fault4                               any `json:"fault4"`
	Fault5                               any `json:"fault5"`
	Fault6                               any `json:"fault6"`
	Fault7                               any `json:"fault7"`
	Fault8                               any `json:"fault8"`
	Fault9                               any `json:"fault9"`
	Fault10                              any `json:"fault10"`
	Fault11                              any `json:"fault11"`
	Fault12                              any `json:"fault12"`
	Fault13                              any `json:"fault13"`
	Fault14                              any `json:"fault14"`
	Fault15                              any `json:"fault15"`
	Fault16                              any `json:"fault16"`
	Fault17                              any `json:"fault17"`
	Fault18                              any `json:"fault18"`
	Fault19                              any `json:"fault19"`
	Fault20                              any `json:"fault20"`
	Fault21                              any `json:"fault21"`
	Fault22                              any `json:"fault22"`
	Fault23                              any `json:"fault23"`
	Fault24                              any `json:"fault24"`
	Fault25                              any `json:"fault25"`
	Fault26                              any `json:"fault26"`
	Fault27                              any `json:"fault27"`
}
