package main

import (
	"context"
	"os/signal"
	"syscall"

	"foxess.beech/config"
	"foxess.beech/forwarder"
	"foxess.beech/logger"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.<PERSON>rror())
	}

	logger.Init(cfg)
	log := logger.GetLogger()

	// 创建消息桥接器
	messageBridge := forwarder.NewMessageBridge(cfg)

	// 启动消息桥接服务
	if err := messageBridge.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start message bridge")
	}
	defer messageBridge.Stop()

	ctx, cancel := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer cancel()

	<-ctx.Done()
	log.Info().Msg("Shutdown signal received, graceful shutdown initiated")
}
