package redis

import (
	"context"
	"strings"
	"time"

	"foxess.beech/config"
	"foxess.beech/logger"
	"github.com/go-redis/redis/v8"
	"github.com/rs/zerolog"
)

const (
	DEVICE_INDEX_HUB_KEY = "device:index:hub:hash"
	MODULE_INDEX_HUB_KEY = "module:index:hub:hash"
)

// RedisClient wraps Redis functionality
type RedisClient struct {
	singleClient  *redis.Client        // 单机redis
	clusterClient *redis.ClusterClient // 集群版本redis
	config        *config.RedisConfig
	logger        zerolog.Logger
	enabled       bool
}

// NewRedisClient creates a new Redis client
func NewRedisClient(cfg *config.RedisConfig) *RedisClient {
	log := logger.GetLogger()

	client := &RedisClient{
		config:  cfg,
		logger:  log,
		enabled: cfg.Enabled,
	}

	if !cfg.Enabled {
		log.Info().Msg("Redis is disabled")
		return client
	}

	if cfg.ClusterEnabled {
		client.initCluster()
	} else {
		client.initSingle()
	}

	return client
}

// initCluster initializes Redis cluster client
func (r *RedisClient) initCluster() {
	if r.config.ClusterAddress == "" {
		r.logger.Fatal().Msg("Redis cluster address is required when cluster is enabled")
		return
	}

	addrs := strings.Split(r.config.ClusterAddress, ";")
	r.clusterClient = redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:        addrs,
		PoolSize:     r.config.PoolSize,
		MinIdleConns: r.config.MinIdleConns,
		Password:     r.config.ClusterPassword,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := r.clusterClient.Ping(ctx).Err(); err != nil {
		r.logger.Fatal().Err(err).Str("address", r.config.ClusterAddress).Msg("Failed to connect to Redis cluster")
		return
	}

	r.logger.Info().Str("address", r.config.ClusterAddress).Msg("Redis cluster connected successfully")
}

// initSingle initializes single Redis client
func (r *RedisClient) initSingle() {
	if r.config.Address == "" {
		r.logger.Fatal().Msg("Redis address is required when single mode is enabled")
		return
	}

	r.singleClient = redis.NewClient(&redis.Options{
		Addr:         r.config.Address,
		PoolSize:     r.config.PoolSize,
		MinIdleConns: r.config.MinIdleConns,
		Password:     r.config.Password,
		DB:           r.config.DB,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := r.singleClient.Ping(ctx).Err(); err != nil {
		r.logger.Fatal().Err(err).Str("address", r.config.Address).Msg("Failed to connect to Redis")
		return
	}

	r.logger.Info().Str("address", r.config.Address).Msg("Redis connected successfully")
}

// GetDevHubIndex gets device hub index from Redis
func (r *RedisClient) GetDevHubIndex(sn string) string {
	if !r.enabled {
		return "0"
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	var cmd *redis.StringCmd
	if r.config.ClusterEnabled {
		cmd = r.clusterClient.HGet(ctx, DEVICE_INDEX_HUB_KEY, sn)
	} else {
		cmd = r.singleClient.HGet(ctx, DEVICE_INDEX_HUB_KEY, sn)
	}

	result, err := cmd.Result()
	r.logger.Debug().Str("sn", sn).Str("result", result).Err(err).Msg("Redis GetDevHubIndex")

	if err != nil || len(result) == 0 {
		return "0"
	}

	return result
}

// GetModHubIndex gets module hub index from Redis
func (r *RedisClient) GetModHubIndex(sn string) string {
	if !r.enabled {
		return "0"
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	var cmd *redis.StringCmd
	if r.config.ClusterEnabled {
		cmd = r.clusterClient.HGet(ctx, MODULE_INDEX_HUB_KEY, sn)
	} else {
		cmd = r.singleClient.HGet(ctx, MODULE_INDEX_HUB_KEY, sn)
	}

	result, err := cmd.Result()
	r.logger.Debug().Str("sn", sn).Str("result", result).Err(err).Msg("Redis GetModHubIndex")

	if err != nil || len(result) == 0 {
		return "0"
	}

	return result
}

// Close closes Redis connections
func (r *RedisClient) Close() error {
	if !r.enabled {
		return nil
	}

	if r.config.ClusterEnabled && r.clusterClient != nil {
		return r.clusterClient.Close()
	} else if r.singleClient != nil {
		return r.singleClient.Close()
	}

	return nil
}

// IsEnabled returns whether Redis is enabled
func (r *RedisClient) IsEnabled() bool {
	return r.enabled
}
