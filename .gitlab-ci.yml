stages:
  - build
  - deploy

variables:
  DOCKER_HUB_URL: swr.cn-east-2.myhuaweicloud.com
  MY_HUB_URL: **************:5000
  DOCKER_HUB_ORG: foxess
  PROJECT_NAME: foxess.beech
  GIT_SSH_URL: **************************

before_script:
  - rm -rf ../library/
  - git clone $GIT_SSH_URL:basic/go/microbus.git ../library/foxess.microbus.go

compile-job:
  stage: build
  script:
    - echo "complile build"
    - go mod tidy
    - go clean &&  go build

#打包本地测试服务器
docker-job:
  stage: deploy
  script:
    - go mod tidy
    - go clean && CGO_ENABLED=0 go build
    - ls -la |grep $PROJECT_NAME
    - echo "docker build for test"
    - docker build -t  $PROJECT_NAME:latest .
    - docker tag $PROJECT_NAME:latest $MY_HUB_URL/$PROJECT_NAME:latest
    - docker push $MY_HUB_URL/$PROJECT_NAME:latest

#如果有版本，则推送到hub
prod-deploy:
  stage: deploy
  script:
    - echo "docker build for prod"
    - docker tag $PROJECT_NAME:latest $DOCKER_HUB_URL/$DOCKER_HUB_ORG/$PROJECT_NAME:$CI_COMMIT_TAG
    - docker push $DOCKER_HUB_URL/$DOCKER_HUB_ORG/$PROJECT_NAME:$CI_COMMIT_TAG
    - docker image rm $DOCKER_HUB_URL/$DOCKER_HUB_ORG/$PROJECT_NAME:$CI_COMMIT_TAG -f
  rules:
    - if: $CI_COMMIT_TAG
