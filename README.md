# BEECH

Vamo 热泵实时数据转发和双向设置请求/响应处理。

### 消息流向

**实时数据流**: `MicroBus` → `BEECH` → `MQTT`
- Topic: `$system/realdata/beech/+/type/+/json` → `vamo/foxess/device/realData/v0/{devSN}/json`

**设置请求流**: `MQTT` → `BEECH` → `Hub` → `BEECH` → `MQTT`

设置
- 请求: `vamo/foxess/device/set/v0/{devSN}/req`
- 响应: `vamo/foxess/device/set/v0/{devSN}/resp`

读取
- 请求: `vamo/foxess/device/get/v0/{devSN}/req`
- 响应: `vamo/foxess/device/get/v0/{devSN}/resp`

### 环境变量配置

### MicroBus 配置
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `KAFKA_BROKERS` | Kafka broker 地址 | `localhost:9092` | `kafka1:9092,kafka2:9092` |
| `MICROBUS_QUEUE_SIZE` | 异步队列大小 | `1000` | `2000` |

### MQTT 配置
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `MQTT_BROKERS` | MQTT broker 地址 | `tcp://localhost:1883` | `tcp://mqtt.example.com:1883` |
| `MQTT_CLIENT_ID` | 客户端 ID | `BEECH-forwarder` | `BEECH-prod-001` |
| `MQTT_USER` | 用户名 (可选) | - | `admin` |
| `MQTT_PASSWORD` | 密码 (可选) | - | `password123` |
| `MQTT_QOS` | QoS 级别 | `0` | `0`, `1`, `2` |
| `MQTT_TIMEOUT` | 连接超时 | `30s` | `60s` |
| `MQTT_SUBSCRIBE_TOPICS` | 订阅的设置请求 topic | `foxess/device/setting/v0/+/req` | 逗号分隔 |

### Hub 配置
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `HUB_GROUP` | Hub 组名称 | `hub` | `production` |
| `HUB_CLUSTER_ENABLE` | 是否启用Hub集群 | `enable` | `disable` |
| `HUB_TIMEOUT` | 请求超时时间 | `30` | `60` |

### Redis 配置
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `REDIS_ENABLED` | 是否启用Redis | `false` | `true` |
| `REDIS_ADDRESS` | Redis地址 | - | `localhost:6379` |
| `REDIS_PASSWORD` | Redis密码 | - | `password123` |
| `REDIS_DB` | Redis数据库编号 | `0` | `1` |
| `REDIS_CLUSTER_ENABLED` | 是否启用Redis集群 | `false` | `true` |
| `REDIS_CLUSTER_ADDRESS` | Redis集群地址 | - | `node1:6379;node2:6379` |
| `REDIS_CLUSTER_PASSWORD` | Redis集群密码 | - | `cluster_password` |
| `REDIS_POOL_SIZE` | 连接池大小 | `128` | `256` |
| `REDIS_MIN_IDLE_CONNS` | 最小空闲连接数 | `32` | `64` |

### 日志配置
| 变量名 | 说明 | 默认值 | 可选值 |
|--------|------|--------|--------|
| `LOG_LEVEL` | 日志级别 | `info` | `trace`, `debug`, `info`, `warn`, `error`, `fatal`, `panic` |
| `LOG_FORMAT` | 日志格式 | `console` | `json`, `console` |