# BEECH (High Performance Forwarder)

Vamo 热泵实时数据转发和双向设置请求/响应处理。

### 消息流向

**实时数据流**: `MicroBus` → `BEECH` → `MQTT`
- Topic: `$system/realdata/+/+/type/+/json` → `foxess/device/realData/v0/{devSN}/json`

**设置请求流**: `MQTT` → `BEECH` → `Hub` → `BEECH` → `MQTT`
- 请求: `foxess/device/setting/v0/{devSN}/req`
- 响应: `foxess/device/setting/v0/{devSN}/resp`

### 环境变量配置

### MicroBus 配置
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `KAFKA_BROKERS` | Kafka broker 地址 | `localhost:9092` | `kafka1:9092,kafka2:9092` |
| `MICROBUS_TOPICS` | 订阅的 topic 模式 | `$system/realdata/+/+/type/+/json` | 逗号分隔多个模式 |
| `MICROBUS_QUEUE_SIZE` | 异步队列大小 | `1000` | `2000` |

### MQTT 配置
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `MQTT_BROKER` | MQTT broker 地址 | `tcp://localhost:1883` | `tcp://mqtt.example.com:1883` |
| `MQTT_CLIENT_ID` | 客户端 ID | `BEECH-forwarder` | `BEECH-prod-001` |
| `MQTT_USERNAME` | 用户名 (可选) | - | `admin` |
| `MQTT_PASSWORD` | 密码 (可选) | - | `password123` |
| `MQTT_QOS` | QoS 级别 | `0` | `0`, `1`, `2` |
| `MQTT_TIMEOUT` | 连接超时 | `30s` | `60s` |
| `MQTT_SUBSCRIBE_TOPICS` | 订阅的设置请求 topic | `foxess/device/setting/v0/+/req` | 逗号分隔 |

### Hub 配置
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `HUB_GROUP` | Hub 组名称 | `beech` | `production` |
| `HUB_SET_RESOURCE` | 设置资源模式 | `$system/set/+/+/type/+/json` | 自定义模式 |
| `HUB_TIMEOUT` | 请求超时时间 | `30s` | `60s` |

### 日志配置
| 变量名 | 说明 | 默认值 | 可选值 |
|--------|------|--------|--------|
| `LOG_LEVEL` | 日志级别 | `info` | `trace`, `debug`, `info`, `warn`, `error`, `fatal`, `panic` |
| `LOG_FORMAT` | 日志格式 | `console` | `json`, `console` |