package forwarder

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"foxess.beech/config"
	"foxess.beech/logger"
	"foxess.beech/types"

	fmicrobus "foxess.cloud/fmicrobus"
	"foxess.cloud/mop"
	"github.com/rs/zerolog"
)

const (
	Success = 0 //成功

	ParametersParseError = 40256 //参数无法被正确解析
	RecvMessageError     = 40258 //收取消息发生异常
	ParseMessageError    = 40259 //消息无法被正常解析
	ResultHasNoData      = 40302 //当前时段无数据
	SendMessageError     = 41204 //生成或发送报文出错
)

// MessageBridge 处理MicroBus和MQTT之间的消息桥接
type MessageBridge struct {
	microbus *fmicrobus.MicroBus
	mqtt     *mop.Engine
	config   *config.Config
	logger   zerolog.Logger
}

// HubSetResponse 表示hub设置响应
type HubSetResponse struct {
	Errno  int            `json:"errno"`
	Result map[string]any `json:"result"`
}

// HubGetRequest 表示hub获取请求
type HubGetRequest struct {
	Key            string `json:"key"`
	HasVersionHead int    `json:"hasVersionHead"`
}

// HubGetResponse 表示hub获取响应
type HubGetResponse struct {
	Errno  int       `json:"errno"`
	Result *HubValue `json:"result"`
}

// HubValue 表示从hub返回的值
type HubValue struct {
	SN        string         `json:"sn"`
	Version   string         `json:"version"`
	Key       string         `json:"key"`
	Values    map[string]any `json:"values"`
	RawValues string         `json:"rawValues"`
}

// SetRequestMessage 表示设置请求消息
type SetRequestMessage struct {
	DevSN   string
	Request *types.MqttRequest
}

type Data struct {
	SN         string               `json:"sn"`         //设备SN
	ModuleSN   string               `json:"moduleSN"`   //模块sn
	Properties map[string]*Property `json:"properties"` //属性值
	Info       *Info                `json:"info"`       //设备信息
	ExInfo     *ExInfo              `json:"exInfo"`     //对外版本

}

// ExInfo 对外版本
type ExInfo struct {
	MasterVersion  string
	SlaveVersion   string
	ManagerVersion string
	AFCIVersion    string
}

// Info 新版本的设备属性信息
type Info struct {
	MasterVersion  string  `json:"masterVersion"`  //主CPU版本
	SlaveVersion   string  `json:"slaveVersion"`   //副CPU版本
	ManagerVersion string  `json:"managerVersion"` //HMI主CPU版本
	AFCIVersion    string  `json:"afciVersion"`    //AFCI版本
	DeviceFactory  uint16  `json:"deviceFactory"`  //厂商 0麦田温州 1麦田无锡
	ProductType    string  `json:"productType"`    //设备产品系列
	DeviceType     string  `json:"deviceType"`     //设备机型
	Capacity       float64 `json:"capacity"`       //设备容量
}

type Property struct {
	Value     any    `json:"value"`
	Timestamp int64  `json:"timestamp"`
	Unit      string `json:"unit"`
}

// NewMessageBridge 创建新的消息桥接器
func NewMessageBridge(cfg *config.Config) *MessageBridge {
	log := logger.GetLogger()

	// 创建 MicroBus 客户端
	microbusClient := fmicrobus.New(cfg.MicroBus.Brokers)

	// 创建 MQTT 客户端
	mopConfig := &mop.Config{
		ClientID: cfg.MQTT.ClientID,
		Broker:   cfg.MQTT.Broker,
		Username: cfg.MQTT.Username,
		Password: cfg.MQTT.Password,
	}
	mqttEngine := mop.New(mopConfig)

	return &MessageBridge{
		microbus: microbusClient,
		mqtt:     mqttEngine,
		config:   cfg,
		logger:   log,
	}
}

// Start 启动消息桥接服务
func (mb *MessageBridge) Start() error {
	mb.mqtt.Subscribe("foxess/device/:method/v0/:devSN/req", mb.config.MQTT.QoS, mb.handleMQTTRequest)

	// 启动 MQTT 引擎
	mb.mqtt.Run()

	mb.microbus.WaitAsync("$system/realdata/heatpump/+/type/+/json", func(mmsg *fmicrobus.MicroMessage) {
		resource := mmsg.GetResource()
		payload := mmsg.GetPayload()
		timestampInt := mmsg.GetTimestamp()
		timestamp := time.Unix(timestampInt, 0)

		mb.logger.Debug().Str("resource", resource).Time("timestamp", timestamp).Int("payload_size", len(payload)).Msg("Received realtime data from microbus")

		mb.handleRealtimeMessage(resource, payload, timestamp)
	}, mb.config.MicroBus.QueueSize)

	mb.logger.Info().Msg("Message bridge started successfully")
	return nil
}

// Stop 停止消息桥接服务
func (mb *MessageBridge) Stop() {
	mb.logger.Info().Msg("Stopping MessageBridge")
}

// Publish 发布消息到MQTT
func (mb *MessageBridge) Publish(topic string, payload []byte) error {
	mb.logger.Debug().Str("topic", topic).Int("payload_size", len(payload)).Msg("Publishing MQTT message")

	mb.mqtt.Publish(topic, mb.config.MQTT.QoS, false, payload)

	mb.logger.Debug().Str("topic", topic).Msg("MQTT message published successfully")

	return nil
}

// PublishMqttResponse 发布设置响应
func (mb *MessageBridge) PublishMqttResponse(method, devSN string, resp *types.SetResponse) error {
	// 构建响应topic: foxess/device/set/v0/{devSN}/resp
	respTopic := fmt.Sprintf("foxess/device/%s/v0/%s/resp", method, devSN)

	respData, err := json.Marshal(resp)
	if err != nil {
		mb.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to marshal set response")
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	if err := mb.Publish(respTopic, respData); err != nil {
		mb.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to publish set response")
		return fmt.Errorf("failed to publish response: %w", err)
	}

	mb.logger.Info().Str("devSN", devSN).Uint32("id", resp.ID).Str("data", string(respData)).Msg("Published response")
	return nil
}

// handleRealtimeMessage 处理实时数据消息并转发到MQTT
func (mb *MessageBridge) handleRealtimeMessage(resource string, payload []byte, timestamp time.Time) {
	mb.logger.Debug().Str("resource", resource).Time("timestamp", timestamp).Int("payload_size", len(payload)).Msg("Processing realtime data from MicroBus")

	// 解析resource路径: "$system/realdata/heatpump/{devSN}/type/{dataType}/json"
	ss := strings.Split(resource, "/")
	if len(ss) != 7 {
		mb.logger.Error().Str("resource", resource).Msg("Invalid resource format")
		return
	}

	// 验证是实时数据消息
	if ss[1] != "realdata" {
		mb.logger.Error().Str("resource", resource).Msg("Not a realtime data message")
		return
	}

	// 从resource路径中提取devSN和dataType
	devSN := ss[3]
	dataType := ss[5]
	if dataType == "" {
		dataType = "json" // 默认数据类型
	}

	mb.logger.Info().Str("devSN", devSN).Str("dataType", dataType).Time("timestamp", timestamp).Msg("Forwarding realdata message to MQTT")

	// 解析和转换数据
	var data Data
	if err := json.Unmarshal(payload, &data); err != nil {
		mb.logger.Error().Err(err).Str("resource", resource).Msg("Failed to parse payload")
		return
	}

	// 转换为简单的键值对
	m := make(map[string]any)
	for k, p := range data.Properties {
		m[k] = getPropertyValue(p)
	}

	// 重新序列化
	newPayload, err := json.Marshal(m)
	if err != nil {
		mb.logger.Error().Err(err).Str("resource", resource).Msg("Failed to marshal payload")
		return
	}

	// 发布到MQTT
	topic := fmt.Sprintf("foxess/device/realData/v0/%s/json", devSN)
	if err := mb.Publish(topic, newPayload); err != nil {
		mb.logger.Error().Err(err).Str("devSN", devSN).Str("dataType", dataType).Str("topic", topic).Msg("Failed to publish realdata to MQTT")
		return
	}

	mb.logger.Debug().Str("devSN", devSN).Str("dataType", dataType).Msg("Realdata message forwarded successfully")
}

// handleMQTTRequest 处理MQTT设置请求
func (mb *MessageBridge) handleMQTTRequest(ctx *mop.Context) {
	topic := ctx.Topic
	payload := ctx.Message.Payload()

	mb.logger.Debug().Str("topic", topic).Str("payload", string(payload)).Msg("Received set request")

	devSN := ctx.Param("devSN")
	method := ctx.Param("method")
	if devSN == "" || method == "" {
		mb.logger.Error().Str("topic", topic).Msg("invalid topic format")
		return
	}

	// 解析请求数据
	var req types.MqttRequest
	if err := json.Unmarshal(payload, &req); err != nil {
		mb.logger.Error().Err(err).Str("topic", topic).Msg("Failed to unmarshal set request")
		return
	}

	// 创建请求消息并异步处理
	reqMsg := &SetRequestMessage{
		DevSN:   devSN,
		Request: &req,
	}

	go func() {
		if err := mb.processMqttRequest(method, reqMsg); err != nil {
			mb.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", req.ID).Msg("Failed to process set request")
		}
	}()
}

// processMqttRequest 处理设置请求
func (mb *MessageBridge) processMqttRequest(method string, reqMsg *SetRequestMessage) error {
	mb.logger.Info().Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Str("base64_data", reqMsg.Request.Frame).Msg("Handling set request from MQTT")

	// 解码 base64 frame 数据为十六进制字符串
	hexData, err := base64.StdEncoding.DecodeString(reqMsg.Request.Frame)
	if err != nil {
		mb.logger.Error().Err(err).Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Str("base64_data", reqMsg.Request.Frame).Msg("Failed to decode base64 frame data")

		// 返回解码错误响应
		resp := &types.SetResponse{
			ID:    reqMsg.Request.ID,
			Errno: 400, // 参数错误
		}
		return mb.PublishMqttResponse(method, reqMsg.DevSN, resp)
	}

	hexString := string(hexData)
	mb.logger.Debug().Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Str("hex_data", hexString).Msg("Decoded base64 frame to hex string")

	// 调用Hub处理设置请求，传递十六进制数据
	errno, responseHexString, err := mb.SetDataWithHex(reqMsg.DevSN, hexString)
	if err != nil {
		mb.logger.Error().Err(err).Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Msg("Failed to call Hub for set request")

		resp := &types.SetResponse{
			ID:    reqMsg.Request.ID,
			Errno: errno,
		}
		return mb.PublishMqttResponse(method, reqMsg.DevSN, resp)
	}

	// 将Hub响应的十六进制字符串编码为base64
	responseBase64 := base64.StdEncoding.EncodeToString([]byte(responseHexString))

	// 转换Hub响应为MQTT响应
	resp := &types.SetResponse{
		ID:    reqMsg.Request.ID,
		Errno: errno,
	}

	mb.logger.Info().Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Str("hex_response", responseHexString).Str("base64_response", responseBase64).Msg("Set request processed")

	return mb.PublishMqttResponse(method, reqMsg.DevSN, resp)
}

// SetDataWithHex 向hub发送十六进制数据设置请求
func (mb *MessageBridge) SetDataWithHex(devSN, hexData string) (int, string, error) {
	mb.logger.Debug().Str("devSN", devSN).Str("hexData", hexData).Msg("Sending hex data request to hub")

	// 创建十六进制数据请求
	req := map[string]any{
		"sn":   devSN,
		"data": hexData,
	}

	reqData, err := json.Marshal(req)
	if err != nil {
		return SendMessageError, "", fmt.Errorf("failed to marshal request: %w", err)
	}

	resource := fmt.Sprintf(mb.config.Hub.SetResource, devSN)

	mb.logger.Debug().Str("resource", resource).Str("group", mb.config.Hub.Group).Msg("Sending hex data RequestGroupAll to hub")

	// 使用底层的MicroBus实例
	msg := mb.microbus.RequestGroupAll(mb.config.Hub.Group, resource, "", reqData, mb.config.Hub.Timeout)
	if msg == nil {
		return RecvMessageError, "", fmt.Errorf("failed to receive hub message: %w", err)
	}

	var result HubSetResponse
	if err := json.Unmarshal(msg.GetPayload(), &result); err != nil {
		return ParseMessageError, "", fmt.Errorf("failed to unmarshal hub result: %w", err)
	}
	if result.Errno != 0 {
		return result.Errno, "", fmt.Errorf("errno: %w", err)
	}

	var decodeData string
	di, ok := result.Result["data"]
	if !ok {
		return Success, "", nil
	}

	decodeData, ok = di.(string)
	if !ok {
		return ParametersParseError, "", fmt.Errorf("error: Coerce data to string fail")
	}
	data, err := base64.StdEncoding.DecodeString(decodeData)

	mb.logger.Debug().Str("devSN", devSN).Int("errno", result.Errno).Str("data", string(data)).Msg("Received hex data response from hub")

	return Success, string(data), nil
}

func getPropertyValue(value *Property) any {
	if value != nil {
		return value.Value
	}

	return nil
}
