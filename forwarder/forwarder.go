package forwarder

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	fmicrobus "foxess.cloud/fmicrobus"
	"foxess.cloud/mop"
	"github.com/rs/zerolog"

	"foxess.beech/config"
	"foxess.beech/logger"
	"foxess.beech/redis"
	"foxess.beech/types"
)

const (
	Success = 0 //成功

	ParametersParseError = 40256 //参数无法被正确解析
	RecvMessageError     = 40258 //收取消息发生异常
	ParseMessageError    = 40259 //消息无法被正常解析
	ResultHasNoData      = 40302 //当前时段无数据
	SendMessageError     = 41204 //生成或发送报文出错

	HubBothWaySendResource = `$system/device/setting/bothWaySend/%s/c/v0/json`
)

// MessageBridge 处理MicroBus和MQTT之间的消息桥接
type MessageBridge struct {
	microbus *fmicrobus.MicroBus
	mqtt     *mop.Engine
	config   *config.Config
	redis    *redis.RedisClient
	logger   zerolog.Logger
}

// HubSetResponse 表示hub设置响应
type HubSetResponse struct {
	Errno  int            `json:"errno"`
	Result map[string]any `json:"result"`
}

type SendCustomArgs struct {
	SN      string `json:"sn"`
	DevSN   string `json:"devSN"`
	Code    byte   `json:"code"`
	Timeout int    `json:"timeout"`
	Data    string `json:"data"`
}

type Property struct {
	Value     any    `json:"value"`
	Timestamp int64  `json:"timestamp"`
	Unit      string `json:"unit"`
}

// NewMessageBridge 创建新的消息桥接器
func NewMessageBridge(cfg *config.Config) *MessageBridge {
	log := logger.GetLogger()

	// 创建 MicroBus 客户端
	microbusClient := fmicrobus.New(cfg.MicroBus.Brokers)

	// 创建 MQTT 客户端
	mopConfig := &mop.Config{
		ClientID:           cfg.MQTT.ClientID,
		Broker:             cfg.MQTT.Broker,
		Username:           cfg.MQTT.Username,
		Password:           cfg.MQTT.Password,
		CertFile:           cfg.MQTT.CertFile,
		KeyFile:            cfg.MQTT.PrivKey,
		InsecureSkipVerify: true,
	}
	mqttEngine := mop.New(mopConfig)

	// 创建 Redis 客户端
	redisClient := redis.NewRedisClient(&cfg.Redis)

	return &MessageBridge{
		microbus: microbusClient,
		mqtt:     mqttEngine,
		config:   cfg,
		redis:    redisClient,
		logger:   log,
	}
}

// Start 启动消息桥接服务
func (mb *MessageBridge) Start() error {
	mb.mqtt.Subscribe("vamo/foxess/device/:method/v0/:devSN/req", mb.config.MQTT.QoS, mb.handleMQTTRequest)

	// 启动 MQTT 引擎
	mb.mqtt.Run()

	mb.microbus.WaitAsync("$system/realdata/beech/+/type/+/json", func(mmsg *fmicrobus.MicroMessage) {
		resource := mmsg.GetResource()
		payload := mmsg.GetPayload()
		timestampInt := mmsg.GetTimestamp()
		timestamp := time.Unix(timestampInt, 0)

		mb.logger.Info().Str("resource", resource).Time("timestamp", timestamp).Int("payload_size", len(payload)).Msg("Received realtime data from microbus")

		mb.handleRealtimeMessage(resource, payload, timestamp)
	}, mb.config.MicroBus.QueueSize)

	mb.logger.Info().Msg("Message bridge started successfully")
	return nil
}

// Stop 停止消息桥接服务
func (mb *MessageBridge) Stop() {
	mb.logger.Info().Msg("Stopping MessageBridge")

	// 关闭 Redis 连接
	if mb.redis != nil {
		if err := mb.redis.Close(); err != nil {
			mb.logger.Error().Err(err).Msg("Failed to close Redis connection")
		}
	}
}

// Publish 发布消息到MQTT
func (mb *MessageBridge) Publish(topic string, payload []byte) error {
	mb.mqtt.Publish(topic, mb.config.MQTT.QoS, false, payload)

	return nil
}

// handleRealtimeMessage 处理实时数据消息并转发到MQTT
func (mb *MessageBridge) handleRealtimeMessage(resource string, payload []byte, timestamp time.Time) {
	// 解析resource路径: "$system/realdata/beech/{devSN}/type/{dataType}/json"
	ss := strings.Split(resource, "/")
	if len(ss) != 7 {
		mb.logger.Error().Str("resource", resource).Msg("Invalid resource format")
		return
	}

	// 验证是实时数据消息
	if ss[1] != "realdata" {
		mb.logger.Error().Str("resource", resource).Msg("Not a realtime data message")
		return
	}

	// 从resource路径中提取devSN和dataType
	devSN := ss[3]
	dataType := ss[5]
	if dataType == "" {
		dataType = "json" // 默认数据类型
	}

	var m map[string]any
	if err := json.Unmarshal(payload, &m); err != nil {
		mb.logger.Error().Err(err).Str("resource", resource).Msg("Failed to unmarshal payload")
		return
	}

	delete(m, "isHistory")
	delete(m, "protocol")

	data, _ := json.Marshal(m)

	// 发布到MQTT
	topic := fmt.Sprintf("vamo/foxess/device/realData/v0/%s/json", devSN)
	if err := mb.Publish(topic, data); err != nil {
		mb.logger.Error().Err(err).Str("devSN", devSN).Str("dataType", dataType).Str("topic", topic).Msg("Failed to publish realdata to MQTT")
		return
	}

	mb.logger.Info().Str("devSN", devSN).Str("dataType", dataType).Msg("Realdata message forwarded successfully")
}

// handleMQTTRequest 处理MQTT设置请求
func (mb *MessageBridge) handleMQTTRequest(ctx *mop.Context) {
	topic := ctx.Topic
	payload := ctx.Message.Payload()
	devSN := ctx.Param("devSN")
	method := ctx.Param("method")

	mb.logger.Info().Str("topic", topic).Str("payload", string(payload)).Msg("Received mqtt request")

	go func() {
		if devSN == "" || method == "" {
			mb.logger.Error().Str("topic", topic).Msg("invalid topic format")
			return
		}

		id, errno, frame, err := mb.processMqttRequest(method, devSN, payload)
		if err != nil {
			mb.logger.Error().Err(err).Str("devSN", devSN).Int("id", id).Msg("Failed to process set request")
		}

		var resp any
		if method == "set" {
			resp = types.SetResponse{
				ID:    id,
				Errno: errno,
			}
		} else {
			resp = types.GetResponse{
				ID:    id,
				Errno: errno,
				Frame: base64.StdEncoding.EncodeToString(frame),
			}
		}

		// 发送响应
		respData, err := json.Marshal(resp)
		if err != nil {
			mb.logger.Error().Err(err).Str("devSN", devSN).Any("id", id).Msg("Failed to marshal set response")
			return
		}

		respTopic := fmt.Sprintf("vamo/foxess/device/%s/v0/%s/resp", method, devSN)
		if err := mb.Publish(respTopic, respData); err != nil {
			mb.logger.Error().Err(err).Str("devSN", devSN).Any("id", id).Msg("Failed to publish set response")
			return
		}

		mb.logger.Info().Str("devSN", devSN).Any("id", id).Str("data", string(respData)).Msg("Published response")
	}()
}

// processMqttRequest 处理设置请求
func (mb *MessageBridge) processMqttRequest(method, devSN string, payload []byte) (int, int, []byte, error) {
	// 解析请求数据
	var req types.MqttRequest
	if err := json.Unmarshal(payload, &req); err != nil {
		return 0, ParametersParseError, nil, fmt.Errorf("failed to unmarshal mqtt request: %w", err)
	}

	hexData, err := base64.StdEncoding.DecodeString(req.Frame)
	if err != nil {
		return req.ID, ParametersParseError, nil, fmt.Errorf("failed to decode base64 frame data: %w", err)
	}

	var code byte = 0x11
	if method == "set" {
		code = 0x12
	}
	hubReq := SendCustomArgs{
		DevSN:   devSN, // 热泵外主机SN
		Code:    code,
		Timeout: 10,
		Data:    base64.StdEncoding.EncodeToString(hexData),
	}

	errno, responseData, err := mb.SetDataWithHex(devSN, hubReq)
	if err != nil {
		return req.ID, errno, responseData, fmt.Errorf("failed to call hub for mqtt request: %w", err)
	}

	return req.ID, Success, responseData, nil
}

func (mb *MessageBridge) SetDataWithHex(devSN string, req SendCustomArgs) (int, []byte, error) {
	reqData, err := json.Marshal(req)
	if err != nil {
		return SendMessageError, nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resource := fmt.Sprintf(HubBothWaySendResource, devSN)

	// 根据设备SN获取对应的Hub组
	hubGroup := mb.GetGroup(mb.config.Hub.Group, devSN, "mod")

	mb.logger.Info().Str("resource", resource).Str("group", hubGroup).Str("data", string(reqData)).Msg("Sending data RequestGroupAll to hub")

	// 使用底层的MicroBus实例
	msg := mb.microbus.RequestGroupAll(hubGroup, resource, "", reqData, mb.config.Hub.Timeout)
	if msg == nil {
		return RecvMessageError, nil, fmt.Errorf("failed to receive hub message: %w", err)
	}

	var result HubSetResponse
	if err := json.Unmarshal(msg.GetPayload(), &result); err != nil {
		return ParseMessageError, nil, fmt.Errorf("failed to unmarshal hub result: %w", err)
	}
	if result.Errno != 0 {
		return result.Errno, nil, fmt.Errorf("hub response errno: %d", result.Errno)
	}

	var decodeData string
	di, ok := result.Result["data"]
	if !ok {
		return Success, nil, nil
	}

	decodeData, ok = di.(string)
	if !ok {
		return ParametersParseError, nil, fmt.Errorf("error: Coerce data to string fail")
	}
	data, err := base64.StdEncoding.DecodeString(decodeData)
	if err != nil {
		return ParseMessageError, nil, fmt.Errorf("failed to decode base64 data: %w", err)
	}

	mb.logger.Info().Str("devSN", devSN).Int("errno", result.Errno).Bytes("data", data).Msg("Received data response from hub")

	return Success, data, nil
}

// GetGroup 根据设备SN和类型获取Hub组名
func (mb *MessageBridge) GetGroup(group, devSN, modType string) string {
	indexStr := "0"
	switch mb.config.Hub.Group {
	case "hub":
		if !mb.config.Hub.ClusterEnable {
			return group
		}
		switch modType {
		case "dev":
			indexStr = mb.redis.GetDevHubIndex(devSN)
		case "mod":
			indexStr = mb.redis.GetModHubIndex(devSN)
		}
		return mb.config.Hub.Group + "-" + indexStr
	default:
		return group
	}
}
