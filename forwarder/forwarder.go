package forwarder

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"foxess.beech/config"
	"foxess.beech/logger"
	"foxess.beech/redis"
	"foxess.beech/types"

	fmicrobus "foxess.cloud/fmicrobus"
	"foxess.cloud/mop"
	"github.com/rs/zerolog"
)

const (
	Success = 0 //成功

	ParametersParseError = 40256 //参数无法被正确解析
	RecvMessageError     = 40258 //收取消息发生异常
	ParseMessageError    = 40259 //消息无法被正常解析
	ResultHasNoData      = 40302 //当前时段无数据
	SendMessageError     = 41204 //生成或发送报文出错

	HubBothWaySendResource = `$system/device/setting/bothWaySend/%s/c/v0/json`
)

// MessageBridge 处理MicroBus和MQTT之间的消息桥接
type MessageBridge struct {
	microbus *fmicrobus.MicroBus
	mqtt     *mop.Engine
	config   *config.Config
	redis    *redis.RedisClient
	logger   zerolog.Logger
}

// HubSetResponse 表示hub设置响应
type HubSetResponse struct {
	Errno  int            `json:"errno"`
	Result map[string]any `json:"result"`
}

// HubGetRequest 表示hub获取请求
type HubGetRequest struct {
	Key            string `json:"key"`
	HasVersionHead int    `json:"hasVersionHead"`
}

// HubGetResponse 表示hub获取响应
type HubGetResponse struct {
	Errno  int       `json:"errno"`
	Result *HubValue `json:"result"`
}

// HubValue 表示从hub返回的值
type HubValue struct {
	SN        string         `json:"sn"`
	Version   string         `json:"version"`
	Key       string         `json:"key"`
	Values    map[string]any `json:"values"`
	RawValues string         `json:"rawValues"`
}

// SetRequestMessage 表示设置请求消息
type SetRequestMessage struct {
	DevSN   string
	Request *types.MqttRequest
}

type Data struct {
	SN         string               `json:"sn"`         //设备SN
	ModuleSN   string               `json:"moduleSN"`   //模块sn
	Properties map[string]*Property `json:"properties"` //属性值
	Info       *Info                `json:"info"`       //设备信息
	ExInfo     *ExInfo              `json:"exInfo"`     //对外版本

}

// ExInfo 对外版本
type ExInfo struct {
	MasterVersion  string
	SlaveVersion   string
	ManagerVersion string
	AFCIVersion    string
}

// Info 新版本的设备属性信息
type Info struct {
	MasterVersion  string  `json:"masterVersion"`  //主CPU版本
	SlaveVersion   string  `json:"slaveVersion"`   //副CPU版本
	ManagerVersion string  `json:"managerVersion"` //HMI主CPU版本
	AFCIVersion    string  `json:"afciVersion"`    //AFCI版本
	DeviceFactory  uint16  `json:"deviceFactory"`  //厂商 0麦田温州 1麦田无锡
	ProductType    string  `json:"productType"`    //设备产品系列
	DeviceType     string  `json:"deviceType"`     //设备机型
	Capacity       float64 `json:"capacity"`       //设备容量
}

type Property struct {
	Value     any    `json:"value"`
	Timestamp int64  `json:"timestamp"`
	Unit      string `json:"unit"`
}

// NewMessageBridge 创建新的消息桥接器
func NewMessageBridge(cfg *config.Config) *MessageBridge {
	log := logger.GetLogger()

	// 创建 MicroBus 客户端
	microbusClient := fmicrobus.New(cfg.MicroBus.Brokers)

	// 创建 MQTT 客户端
	mopConfig := &mop.Config{
		ClientID:           cfg.MQTT.ClientID,
		Broker:             cfg.MQTT.Broker,
		Username:           cfg.MQTT.Username,
		Password:           cfg.MQTT.Password,
		CertFile:           cfg.MQTT.CertFile,
		KeyFile:            cfg.MQTT.PrivKey,
		InsecureSkipVerify: true,
	}

	log.Info().Str("mopConfig", fmt.Sprintf("%v", mopConfig)).Msg("mop config")

	mqttEngine := mop.New(mopConfig)

	// 创建 Redis 客户端
	redisClient := redis.NewRedisClient(&cfg.Redis)

	return &MessageBridge{
		microbus: microbusClient,
		mqtt:     mqttEngine,
		config:   cfg,
		redis:    redisClient,
		logger:   log,
	}
}

// Start 启动消息桥接服务
func (mb *MessageBridge) Start() error {
	mb.mqtt.Subscribe("foxess/device/:method/v0/:devSN/req", mb.config.MQTT.QoS, mb.handleMQTTRequest)

	// 启动 MQTT 引擎
	mb.mqtt.Run()

	mb.microbus.WaitAsync("$system/realdata/heatpump/+/type/+/json", func(mmsg *fmicrobus.MicroMessage) {
		resource := mmsg.GetResource()
		payload := mmsg.GetPayload()
		timestampInt := mmsg.GetTimestamp()
		timestamp := time.Unix(timestampInt, 0)

		mb.logger.Debug().Str("resource", resource).Time("timestamp", timestamp).Int("payload_size", len(payload)).Msg("Received realtime data from microbus")

		mb.handleRealtimeMessage(resource, payload, timestamp)
	}, mb.config.MicroBus.QueueSize)

	mb.logger.Info().Msg("Message bridge started successfully")
	return nil
}

// Stop 停止消息桥接服务
func (mb *MessageBridge) Stop() {
	mb.logger.Info().Msg("Stopping MessageBridge")

	// 关闭 Redis 连接
	if mb.redis != nil {
		if err := mb.redis.Close(); err != nil {
			mb.logger.Error().Err(err).Msg("Failed to close Redis connection")
		}
	}
}

// Publish 发布消息到MQTT
func (mb *MessageBridge) Publish(topic string, payload []byte) error {
	mb.logger.Debug().Str("topic", topic).Int("payload_size", len(payload)).Msg("Publishing MQTT message")

	mb.mqtt.Publish(topic, mb.config.MQTT.QoS, false, payload)

	mb.logger.Debug().Str("topic", topic).Msg("MQTT message published successfully")

	return nil
}

// PublishMqttResponse 发布设置响应
func (mb *MessageBridge) PublishMqttResponse(method, devSN string, resp map[string]any) error {
	// 构建响应topic: foxess/device/set/v0/{devSN}/resp
	respTopic := fmt.Sprintf("foxess/device/%s/v0/%s/resp", method, devSN)

	respData, err := json.Marshal(resp)
	if err != nil {
		mb.logger.Error().Err(err).Str("devSN", devSN).Any("id", resp["id"]).Msg("Failed to marshal set response")
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	if err := mb.Publish(respTopic, respData); err != nil {
		mb.logger.Error().Err(err).Str("devSN", devSN).Any("id", resp["id"]).Msg("Failed to publish set response")
		return fmt.Errorf("failed to publish response: %w", err)
	}

	mb.logger.Info().Str("devSN", devSN).Any("id", resp["id"]).Str("data", string(respData)).Msg("Published response")
	return nil
}

// handleRealtimeMessage 处理实时数据消息并转发到MQTT
func (mb *MessageBridge) handleRealtimeMessage(resource string, payload []byte, timestamp time.Time) {
	mb.logger.Debug().Str("resource", resource).Time("timestamp", timestamp).Int("payload_size", len(payload)).Msg("Processing realtime data from MicroBus")

	// 解析resource路径: "$system/realdata/heatpump/{devSN}/type/{dataType}/json"
	ss := strings.Split(resource, "/")
	if len(ss) != 7 {
		mb.logger.Error().Str("resource", resource).Msg("Invalid resource format")
		return
	}

	// 验证是实时数据消息
	if ss[1] != "realdata" {
		mb.logger.Error().Str("resource", resource).Msg("Not a realtime data message")
		return
	}

	// 从resource路径中提取devSN和dataType
	devSN := ss[3]
	dataType := ss[5]
	if dataType == "" {
		dataType = "json" // 默认数据类型
	}

	mb.logger.Info().Str("devSN", devSN).Str("dataType", dataType).Time("timestamp", timestamp).Msg("Forwarding realdata message to MQTT")

	// 解析和转换数据
	var data Data
	if err := json.Unmarshal(payload, &data); err != nil {
		mb.logger.Error().Err(err).Str("resource", resource).Msg("Failed to parse payload")
		return
	}

	// 转换为简单的键值对
	m := make(map[string]any)
	for k, p := range data.Properties {
		m[k] = getPropertyValue(p)
	}

	// 重新序列化
	newPayload, err := json.Marshal(m)
	if err != nil {
		mb.logger.Error().Err(err).Str("resource", resource).Msg("Failed to marshal payload")
		return
	}

	// 发布到MQTT
	topic := fmt.Sprintf("foxess/device/realData/v0/%s/json", devSN)
	if err := mb.Publish(topic, newPayload); err != nil {
		mb.logger.Error().Err(err).Str("devSN", devSN).Str("dataType", dataType).Str("topic", topic).Msg("Failed to publish realdata to MQTT")
		return
	}

	mb.logger.Debug().Str("devSN", devSN).Str("dataType", dataType).Msg("Realdata message forwarded successfully")
}

// handleMQTTRequest 处理MQTT设置请求
func (mb *MessageBridge) handleMQTTRequest(ctx *mop.Context) {
	topic := ctx.Topic
	payload := ctx.Message.Payload()

	mb.logger.Debug().Str("topic", topic).Str("payload", string(payload)).Msg("Received set request")

	go func() {
		devSN := ctx.Param("devSN")
		method := ctx.Param("method")
		if devSN == "" || method == "" {
			mb.logger.Error().Str("topic", topic).Msg("invalid topic format")
			return
		}

		id, errno, frame, err := mb.processMqttRequest(devSN, payload)
		if err != nil {
			mb.logger.Error().Err(err).Str("devSN", devSN).Int("id", id).Msg("Failed to process set request")
		}

		resp := map[string]any{
			"id":    id,
			"errno": errno,
		}
		if method == "get" {
			resp["frame"] = frame
		}

		// 发送响应
		if err := mb.PublishMqttResponse(method, devSN, resp); err != nil {
			mb.logger.Error().Err(err).Str("devSN", devSN).Int("id", id).Msg("Failed to publish set response")
		}
	}()
}

// processMqttRequest 处理设置请求
func (mb *MessageBridge) processMqttRequest(devSN string, payload []byte) (int, int, string, error) {
	// 解析请求数据
	var req types.MqttRequest
	if err := json.Unmarshal(payload, &req); err != nil {
		return 0, ParametersParseError, "", fmt.Errorf("failed to unmarshal mqtt request: %w", err)
	}

	// 创建请求消息并异步处理
	reqMsg := &SetRequestMessage{
		DevSN:   devSN,
		Request: &req,
	}

	// 解码 base64 frame 数据为十六进制字符串
	hexData, err := base64.StdEncoding.DecodeString(reqMsg.Request.Frame)
	if err != nil {
		return req.ID, ParametersParseError, "", fmt.Errorf("failed to decode base64 frame data: %w", err)
	}

	hexString := string(hexData)

	// 调用Hub处理设置请求，传递十六进制数据
	errno, responseHexString, err := mb.SetDataWithHex(reqMsg.DevSN, hexString)
	if err != nil {
		return req.ID, errno, responseHexString, fmt.Errorf("failed to call hub for set request: %w", err)
	}

	return req.ID, Success, responseHexString, nil
}

// SetDataWithHex 向hub发送十六进制数据设置请求
func (mb *MessageBridge) SetDataWithHex(devSN, hexData string) (int, string, error) {
	mb.logger.Debug().Str("devSN", devSN).Str("hexData", hexData).Msg("Sending hex data request to hub")

	// 创建十六进制数据请求
	req := map[string]any{
		"sn":   devSN,
		"data": hexData,
	}

	reqData, err := json.Marshal(req)
	if err != nil {
		return SendMessageError, "", fmt.Errorf("failed to marshal request: %w", err)
	}

	resource := fmt.Sprintf(HubBothWaySendResource, devSN)

	// 根据设备SN获取对应的Hub组
	hubGroup := mb.GetGroup(mb.config.Hub.Group, devSN, "mod")

	mb.logger.Debug().Str("resource", resource).Str("group", hubGroup).Msg("Sending hex data RequestGroupAll to hub")

	// 使用底层的MicroBus实例
	msg := mb.microbus.RequestGroupAll(hubGroup, resource, "", reqData, mb.config.Hub.Timeout)
	if msg == nil {
		return RecvMessageError, "", fmt.Errorf("failed to receive hub message: %w", err)
	}

	var result HubSetResponse
	if err := json.Unmarshal(msg.GetPayload(), &result); err != nil {
		return ParseMessageError, "", fmt.Errorf("failed to unmarshal hub result: %w", err)
	}
	if result.Errno != 0 {
		return result.Errno, "", fmt.Errorf("errno: %w", err)
	}

	var decodeData string
	di, ok := result.Result["data"]
	if !ok {
		return Success, "", nil
	}

	decodeData, ok = di.(string)
	if !ok {
		return ParametersParseError, "", fmt.Errorf("error: Coerce data to string fail")
	}
	data, err := base64.StdEncoding.DecodeString(decodeData)

	mb.logger.Debug().Str("devSN", devSN).Int("errno", result.Errno).Str("data", string(data)).Msg("Received hex data response from hub")

	return Success, string(data), nil
}

func getPropertyValue(value *Property) any {
	if value != nil {
		return value.Value
	}

	return nil
}

// GetGroup 根据设备SN和类型获取Hub组名
func (mb *MessageBridge) GetGroup(group, devSN, modType string) string {
	indexStr := "0"
	switch mb.config.Hub.Group {
	case "hub":
		if !mb.config.Hub.ClusterEnable {
			return group
		}
		switch modType {
		case "dev":
			indexStr = mb.redis.GetDevHubIndex(devSN)
		case "mod":
			indexStr = mb.redis.GetModHubIndex(devSN)
		}
		return mb.config.Hub.Group + "-" + indexStr
	default:
		return group
	}
}
