# MicroBus Configuration
K8S_ENABLE=true
K8S_MICROBUS_GROUPS=k8s-beech
microbus_groups=beech
KAFKA_BROKERS=localhost:9092
MICROBUS_QUEUE_SIZE=100

# MQTT Configuration
MQTT_BROKERS=tcp://localhost:1883
MQTT_CLIENT_ID=beech-forwarder
MQTT_USER=
MQTT_PASSWORD=
MQTT_CERT=
MQTT_PRIV_KEY=
MQTT_QOS=0
MQTT_TIMEOUT=30s

# Hub Configuration
HUB_GROUP=beech
HUB_CLUSTER_ENABLE=enable
HUB_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=console

# Redis Configuration
REDIS_ENABLED=false
REDIS_ADDRESS=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_CLUSTER_ENABLED=false
REDIS_CLUSTER_ADDRESS=
REDIS_CLUSTER_PASSWORD=
REDIS_POOL_SIZE=128
REDIS_MIN_IDLE_CONNS=32
