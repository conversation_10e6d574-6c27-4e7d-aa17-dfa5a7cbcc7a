package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"foxess.beech/config"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

var Logger zerolog.Logger

// Init initializes the global logger based on configuration
func Init(cfg *config.Config) {
	// Set log level
	level := parseLogLevel(cfg.Log.Level)
	zerolog.SetGlobalLevel(level)

	// Set custom caller format to show only filename:line
	zerolog.CallerMarshalFunc = func(pc uintptr, file string, line int) string {
		// Extract just the filename and line number
		filename := filepath.Base(file)

		// Try to get the directory name too for better context
		dir := filepath.Base(filepath.Dir(file))
		if dir != "." && dir != "/" {
			filename = dir + "/" + filename
		}

		return fmt.Sprintf("%s:%d", filename, line)
	}

	// Configure output format
	if strings.ToLower(cfg.Log.Format) == "console" {
		// Pretty console output for development
		output := zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: time.RFC3339,
		}
		Logger = zerolog.New(output).With().Timestamp().Caller().Logger()
	} else {
		// JSON format for production
		Logger = zerolog.New(os.Stdout).With().Timestamp().Caller().Logger()
	}

	// Set as global logger
	log.Logger = Logger

	Logger.Info().Str("level", cfg.Log.Level).Str("format", cfg.Log.Format).Msg("Logger initialized")
}

// parseLogLevel converts string log level to zerolog level
func parseLogLevel(level string) zerolog.Level {
	switch strings.ToLower(level) {
	case "trace":
		return zerolog.TraceLevel
	case "debug":
		return zerolog.DebugLevel
	case "info":
		return zerolog.InfoLevel
	case "warn", "warning":
		return zerolog.WarnLevel
	case "error":
		return zerolog.ErrorLevel
	case "fatal":
		return zerolog.FatalLevel
	case "panic":
		return zerolog.PanicLevel
	default:
		return zerolog.InfoLevel
	}
}

// GetLogger returns the configured logger instance
func GetLogger() zerolog.Logger {
	return Logger
}
